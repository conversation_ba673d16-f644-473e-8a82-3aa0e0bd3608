#include <stdio.h>
#include <math.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_adc/adc_continuous.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "filters/iir_filter.h"

#define SAMPLE_RATE         20000  // 20 kHz
#define ADC_BUFFER_SIZE     256   // Buffer size for ADC reads
#define TAG                 "ADC_FILTER"

static adc_continuous_handle_t adc_handle = NULL;

static IIRFilter hp_filter;
static IIRFilter lp_filter;

void init_filters() {
    // Option 1: Original coefficients for 16 kHz (effective cutoffs: ~375 Hz high-pass, ~5000 Hz low-pass at 20 kHz)
    // iir_init(&hp_filter, 0.9417f, -1.8834f, 0.9417f, -1.8800f, 0.8819f); // High-pass 300 Hz
    // iir_init(&lp_filter, 0.2066f, 0.4131f, 0.2066f, -0.3695f, 0.1958f); // Low-pass 4000 Hz

    // Option 2: Adjusted coefficients for 20 kHz (maintain 300 Hz high-pass, 4000 Hz low-pass)
    iir_init(&hp_filter, 0.9370f, -1.8740f, 0.9370f, -1.8731f, 0.8749f); // High-pass 300 Hz
    iir_init(&lp_filter, 0.1585f, 0.3170f, 0.1585f, -0.3695f, 0.0035f); // Low-pass 4000 Hz
}

void process_audio_samples(const uint8_t *buffer, size_t len) {
    static int64_t last_log_time = 0;
    static uint32_t debug_counter = 0;

    int64_t current_time = esp_timer_get_time();
    for (int i = 0; i < len; i += sizeof(adc_digi_output_data_t)) {
        if (i + sizeof(adc_digi_output_data_t) > len) {
            ESP_LOGW(TAG, "Incomplete ADC sample, skipping");
            break;
        }
        adc_digi_output_data_t *sample = (adc_digi_output_data_t*)&buffer[i];
        int raw = sample->type1.data; // Valeur brute ADC (0–4095)

        float input = (float)raw - 2048.0f; // Centre autour de 0
        float hp = iir_filter(&hp_filter, input);
        float filtered = iir_filter(&lp_filter, hp);

        // Print raw and filtered values once per second
        if (current_time - last_log_time >= 1000000) { // 1 second in microseconds
            ESP_LOGI(TAG, "Raw ADC value: %d, Filtered: %.2f", raw, filtered);
            last_log_time = current_time;
        }
        debug_counter++;
    }
}

void app_main(void) {
    esp_err_t ret;

    // Initialize NVS (required for ADC)
    ret = nvs_flash_init() ;
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize continuous ADC
    adc_continuous_handle_cfg_t adc_config = {
        .max_store_buf_size = 1024,
        .conv_frame_size = ADC_BUFFER_SIZE,
    };
    ESP_ERROR_CHECK(adc_continuous_new_handle(&adc_config, &adc_handle));

    adc_digi_pattern_config_t pattern = {
        .atten = 2, // Range ~0–3.3V
        .channel = ADC_CHANNEL_6, // GPIO34
        .unit = ADC_UNIT_1,
        .bit_width = ADC_BITWIDTH_12,
    };

    adc_continuous_config_t dig_cfg = {
        .sample_freq_hz = SAMPLE_RATE,
        .conv_mode = ADC_CONV_SINGLE_UNIT_1,
        .format = ADC_DIGI_OUTPUT_FORMAT_TYPE1,
        .pattern_num = 1,
        .adc_pattern = &pattern
    };

    ESP_ERROR_CHECK(adc_continuous_config(adc_handle, &dig_cfg));
    ESP_ERROR_CHECK(adc_continuous_start(adc_handle));

    // Initialize filters
    init_filters();

    // Buffer for ADC reads
    uint8_t result[ADC_BUFFER_SIZE];

    // Main loop
    while (1) {
        uint32_t len = 0;
        ret = adc_continuous_read(adc_handle, result, ADC_BUFFER_SIZE, &len, 1000);
        if (ret == ESP_OK) {
            process_audio_samples(result, len);
        }
        // Small delay to prevent watchdog issues
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}